<?php

declare(strict_types=1);

use App\Models\Property;
use App\Models\User;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin can access admin dashboard', function (): void {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);
    $response->assertSee('Admin Dashboard');
    $response->assertSee('Total Users');
    $response->assertSee('Total Properties');
});

test('non-admin users cannot access admin dashboard', function (): void {
    $user = User::factory()->create([
        'role' => 'seeker',
        'is_active' => true,
    ]);

    $this->actingAs($user);

    $response = $this->get('/admin');
    $response->assertStatus(403);
});

test('admin dashboard shows correct statistics', function (): void {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    // Create test data
    User::factory()->count(5)->create(['role' => 'agent']);
    User::factory()->count(10)->create(['role' => 'seeker']);

    $agent = User::factory()->create(['role' => 'agent']);
    Property::factory()->count(3)->create([
        'user_id' => $agent->id,
        'status' => 'published',
    ]);
    Property::factory()->count(2)->create([
        'user_id' => $agent->id,
        'status' => 'draft',
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);

    // Check if statistics are displayed
    $response->assertSee('16'); // Total users (admin + 5 agents + 10 seekers)
    $response->assertSee('5'); // Total properties
    $response->assertSee('3'); // Published properties
    $response->assertSee('2'); // Pending properties
});

test('admin dashboard shows user role breakdown', function (): void {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    // Create users with different roles
    User::factory()->count(2)->create(['role' => 'admin']);
    User::factory()->count(3)->create(['role' => 'agent']);
    User::factory()->count(5)->create(['role' => 'seeker']);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);

    // Check role breakdown
    $response->assertSee('3'); // Admin users (including the test admin)
    $response->assertSee('3'); // agent users
    $response->assertSee('5'); // Seeker users
});

test('admin dashboard shows recent activity', function (): void {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    $agent = User::factory()->create([
        'role' => 'agent',
        'name' => 'Test agent',
    ]);

    $property = Property::factory()->create([
        'user_id' => $agent->id,
        'title' => 'Test Property',
        'status' => 'published',
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);

    // Check if recent activity is shown
    $response->assertSee('Recent Properties');
    $response->assertSee('Test Property');
    $response->assertSee('Test agent');
});
